"use client";
import Modal from "@/components/Modal";
import RangeSlider from "@/components/RangeSlider";
import SetVehiclePrice from "@/components/SetVehiclePrice";
import VehicleBodyType from "@/components/VehicleBodyType";
import VehicleFeatures from "@/components/VehicleFeatures";
import VehicleMake from "@/components/VehicleMake";
import VehiclePrice from "@/components/VehiclePrice";
import VehicleTrim from "@/components/VehicleTrim";
import { useState } from "react";

const Page = () => {
  const [showModal, setShowModal] = useState(false);
  const [price, setPrice] = useState(0);

  return (
    <div style={{ marginTop: "6rem" }}>
      <Modal
        showModal={true}
        contentMoreThanScreenHeight
        setShowModal={() => setShowModal(false)}
        modalContent={<VehicleMake />}
        // modalContent={<VehicleBodyType  />}
        // modalContent={<VehicleTrim  />}
        // modalContent={<VehiclePrice  />}
        // modalContent={<SetVehiclePrice  />}
        // modalContent={
        //   <RangeSlider
        //     placeholder="Set Maximum Price"
        //     onChange={(value) => {
        //       setPrice(value);
        //       console.log("Price changed to:", value);
        //     }}
        //     inputLabel="Max Price"
        //     min={0}
        //     max={3000000}
        //     inputMax={10000000}
        //     defaultValue={0}
        //     step={50000}
        //   />
        // }
        // modalContent={<VehicleFeatures  />}
      />
    </div>
  );
};

export default Page;
