@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;
.modalOverlay {
  display: flex;
  flex-direction: column;

  align-items: center;
  position: fixed; /* Stays in place when scrolling */
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%); /* Centers the element */
  z-index: 5; /* Ensures it's above other content */
  width: 100vw;
  height: 102vh;
  z-index: 100;
  cursor:
    url("/icons/X.png") 16 0,
    auto; /* Set hotspot (exact clicking point) to 16px from the left */
  overflow-y: auto;
  overflow-x: hidden;
  background-color: $white-four;

  .forceHide {
    display: none !important;
  }

  &::-webkit-scrollbar {
    cursor: auto !important;
    // width: rem(16) !important;
  }

  &::-webkit-scrollbar-track {
    cursor: auto !important;
  }

  &::-webkit-scrollbar-thumb {
    cursor: auto !important;
  }

  &::-webkit-scrollbar-thumb:hover {
    cursor: auto !important;
  }
}

.exitButtonContainer {
  position: absolute;
  left: 95%;
  top: 3%;
  gap: rem(16);
  cursor: pointer;
  width: 100%;
  z-index: 10;
}
.modalContent {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: default;
}

.lessThanScreenHeight {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.moreThanScreenHeight {
  display: flex;
  flex-direction: column;
  align-items: center;
}
