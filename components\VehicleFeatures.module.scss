@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;

.container {
    position: relative;
  // height: 100vh;
  width: 100vw;

  .featuresContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: rem(48);

    .featureGroup {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 75%;
      margin-bottom: rem(54);
      padding: rem(56) rem(100) rem(72) rem(100);
      border-radius: rem(48);
      background-color: $white-three;
      .category {
        font-size: $body;
        font-weight: 500;
        color: $danger;
        margin-bottom: rem(48);
      }

      .features {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: rem(32);
        width: 100%;
        padding: 0 rem(54);
        .feature {
          display: flex;
          align-items: center;
          width: 30%;
          gap: rem(32);
          font-size: rem(14);
        //   margin-bottom: rem(32);
        }
      }
    }
  }
  .buttonContainer {
    display: flex;
    justify-content: center;
    position: sticky;
    bottom: 0;
    // background-color: $white-two;
    box-shadow: 0 rem(4) rem(12) rgba(0, 0, 0, 0.05);
    width: 100vw; 

    .button {
     margin-top: rem(20);
     margin-bottom: rem(48);
    }
  }
}
