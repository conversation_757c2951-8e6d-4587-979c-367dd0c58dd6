@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;

.container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: rem(200);
  // height: 100vh;

  .title {
    font-size: $h4;
    color: $danger;
    font-weight: 500;
    margin-top: rem(48);
    margin-bottom: rem(20);
  }
  .searchContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: rem(20);
    // margin-top: rem(80);
    margin-bottom: rem(80);
  }
  .tabsContainer {
    margin-bottom: rem(20);
  }

  .pillsContainer {
    position: sticky;
    top: 0;
    display: flex;
    justify-content: center;
    width: 100vw;
    padding-top: rem(20);
    z-index: 10;

    // Add the box shadow only when stuck
    &.stuck {
      box-shadow: 0 rem(4) rem(12) rgba(0, 0, 0, 0.05);
    }

    .pillsWrapper {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      gap: rem(14);
      max-width: rem(1200);
      margin-bottom: rem(20);
    }
  }
  .wrapper {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 100%;
    margin-top: rem(20);
    .checkboxContainer {
      position: absolute;
      top: rem(-72);
      right: rem(172);
    }

    .cardsContainer {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      gap: rem(32);
      width: 100%;
      height: 100%;
    }
  }
  .searchButton {
    display: flex;
    justify-content: center;
    position: sticky;
    bottom: 0;
    background-color: $white-two;
    width: 100vw;

    .search {
      margin-top: rem(20);
      margin-bottom: rem(48);
    }
  }
}
