export const vehicleFeaturesData = [
  {
    category: "Safety",
    features: [
      "ABS",
      "Electronic Stability Control",
      "Traction Control",
      "Front Airbags",
      "Side Airbags",
      "Lane Keep Assist",
      "Adaptive Cruise Control",
    ],
  },
  {
    category: "Comfort",
    features: [
      "Automatic Climate Control",
      "Heated Seats",
      "Leather Upholstery",
      "Electric Seat Adjustment",
      "Keyless Entry",
      "Push Start",
    ],
  },
  {
    category: "Technology",
    features: [
      "Touchscreen Infotainment",
      "Apple CarPlay",
      "Android Auto",
      "Bluetooth",
      "Navigation",
      "Digital Instrument Cluster",
    ],
  },
  {
    category: "Exterior",
    features: [
      "LED Headlights",
      "Fog Lamps",
      "Alloy Wheels",
      "Sunroof",
      "Power-Folding Mirrors",
    ],
  },
  {
    category: "Performance",
    features: [
      "Sport Mode",
      "Turbocharged Engine",
      "All-Wheel Drive",
      "Paddle Shifters",
      "Upgraded Suspension",
    ],
  },
];
